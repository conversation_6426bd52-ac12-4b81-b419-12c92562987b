import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from MeshScooter.widgets.customWidget import *
import maya.mel as mel


class UVManipulation(QtWidgets.QDialog):

	def __init__(self, parent=None):
		super(UVManipulation, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connections()


	def create_widgets(self):
		self.UVContainer = Container("UV")

		self.NormalizeUVsButton = IconButton("Normalize UVs","+.png")

		self.CreateIndexesButton = IconButton("Create Indexes UVs","+.png")


	def create_layout(self):

		self.UVContainerLayout = QtWidgets.QVBoxLayout()	
		self.UVContainerLayout.addWidget(self.UVContainer)
		self.UVContainerLayout.setContentsMargins(20,5,11,5)

		self.UVLayout = QtWidgets.QVBoxLayout(self.UVContainer.contentWidget)

		self.UVNameLayout = QtWidgets.QHBoxLayout()
		self.UVNameLayout.addWidget(self.CreateIndexesButton)
		UVNameSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.UVNameLayout.addItem(UVNameSpacer)


		self.UVLayout.addLayout(self.UVNameLayout)

		self.UVLayout.addWidget(SeparatorWidget())

		self.UVContainerLayout.addLayout(self.UVLayout)
		self.setLayout(self.UVContainerLayout)

	def create_connections(self,):
		self.CreateIndexesButton.clicked.connect(self.Create_Indexes_UV)

	def Create_Indexes_UV(self):
		"""
		Create indexed UV sets for all meshes in selected folder's subfolders (ID_00, ID_01, etc.)
		Each mesh gets a new UV set with UV islands scaled to dots positioned at fraction U positions
		"""
		import maya.cmds as cmds
		import re

		# Get selected objects (should be the main folder)
		selected = cmds.ls(selection=True)
		if not selected:
			print("Please select the main folder containing ID_XX subfolders")
			return

		main_folder = selected[0]

		# Get all children of the main folder
		children = cmds.listRelatives(main_folder, children=True, type='transform') or []

		# Filter for ID_XX folders
		id_folders = []
		for child in children:
			if re.match(r'^ID_\d{2}$', child):
				id_folders.append(child)

		if not id_folders:
			print("No ID_XX subfolders found in selected folder")
			return

		print(f"Processing {len(id_folders)} ID folders...")

		for folder in id_folders:
			# Extract the index number from folder name (e.g., "ID_05" -> 5)
			match = re.search(r'ID_(\d{2})', folder)
			if not match:
				continue

			index = int(match.group(1))
			u_position = index / 16.0  # Position on U axis (0-15 mapped to 0.0-0.9375)

			print(f"Processing folder {folder} - Index: {index}, U position: {u_position}")

			# Get all meshes in this folder (recursively)
			meshes = self.get_meshes_in_folder(folder)

			for mesh in meshes:
				self.create_indexed_uv_set(mesh, index, u_position)

		print("Finished creating indexed UV sets")

	def get_meshes_in_folder(self, folder):
		"""
		Recursively get all mesh objects within a folder
		"""
		import maya.cmds as cmds

		meshes = []

		# Get all descendants of the folder
		descendants = cmds.listRelatives(folder, allDescendents=True, type='transform') or []
		descendants.append(folder)  # Include the folder itself

		for obj in descendants:
			# Check if object has mesh shape
			shapes = cmds.listRelatives(obj, shapes=True, type='mesh') or []
			if shapes:
				meshes.append(obj)

		return meshes

	def create_indexed_uv_set(self, mesh, index, u_position):
		"""
		Create a new UV set for the mesh with UVs scaled to a dot at the specified U position
		"""
		import maya.cmds as cmds

		try:
			# Get current UV set
			current_uv_sets = cmds.polyUVSet(mesh, query=True, allUVSets=True)
			if not current_uv_sets:
				print(f"No UV sets found on {mesh}")
				return

			current_uv_set = current_uv_sets[0]  # Use first UV set
			new_uv_set_name = f"IndexedUV_{index:02d}"

			# Create new UV set by copying current one
			cmds.polyUVSet(mesh, copy=True, newUVSet=new_uv_set_name, uvSet=current_uv_set)
			cmds.polyUVSet(mesh, currentUVSet=True, uvSet=new_uv_set_name)

			# Select all faces of the mesh
			face_count = cmds.polyEvaluate(mesh, face=True)
			faces = [f"{mesh}.f[{i}]" for i in range(face_count)]
			cmds.select(faces)

			# Scale UV islands to a single point (scale to 0.001 to avoid complete collapse)
			cmds.polyEditUV(scaleU=0.001, scaleV=0.001, pivotU=0.5, pivotV=0.5)

			# Move the dot to the indexed U position, centered vertically
			cmds.polyEditUV(uValue=u_position, vValue=0.5, relative=False)

			print(f"Created indexed UV set '{new_uv_set_name}' for {mesh} at U={u_position}")

		except Exception as e:
			print(f"Error processing {mesh}: {str(e)}")

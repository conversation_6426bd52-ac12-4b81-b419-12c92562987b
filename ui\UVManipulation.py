import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from MeshScooter.widgets.customWidget import *
import maya.mel as mel


class UVManipulation(QtWidgets.QDialog):

	def __init__(self, parent=None):
		super(UVManipulation, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connections()


	def create_widgets(self):
		self.UVContainer = Container("UV")

		self.NormalizeUVsButton = IconButton("Normalize UVs","+.png")

		self.CreateIndexesButton = IconButton("Create Indexes UVs","+.png")


	def create_layout(self):

		self.UVContainerLayout = QtWidgets.QVBoxLayout()	
		self.UVContainerLayout.addWidget(self.UVContainer)
		self.UVContainerLayout.setContentsMargins(20,5,11,5)

		self.UVLayout = QtWidgets.QVBoxLayout(self.UVContainer.contentWidget)

		self.UVNameLayout = QtWidgets.QHBoxLayout()
		self.UVNameLayout.addWidget(self.CreateIndexesButton)
		UVNameSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.UVNameLayout.addItem(UVNameSpacer)


		self.UVLayout.addLayout(self.UVNameLayout)

		self.UVLayout.addWidget(SeparatorWidget())

		self.UVContainerLayout.addLayout(self.UVLayout)
		self.setLayout(self.UVContainerLayout)

	def create_connections(self,):
		pa

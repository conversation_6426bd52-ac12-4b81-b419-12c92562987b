import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from maya import OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from PrincipalSkinner.widgets.customWidget import *

class CleanerManager(QtWidgets.QDialog):
	def __init__(self, parent=None):
		super(CleanerManager, self).__init__(parent)
		
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connection()

	def create_widgets(self):
		# Manager Reference
		self.cleanManagerContainer = Container("Cleaner Manager")

		self.deleteGroupBox = customFrame()

		self.deleteUnusedButton = QtWidgets.QPushButton("Delete Unused Nodes")
		self.deleteEmptySetsButton = QtWidgets.QPushButton("Delete Empty Sets")
		self.deleteEmptyLayerButton = QtWidgets.QPushButton("Delete Empty Layer")
		
	def create_layout(self):
		# Manager Reference
		self.cleanManagerContainerLayout = QtWidgets.QVBoxLayout()
		# self.cleanManagerContainerLayout.setContentsMargins(0,0,0,0)
		
		self.cleanManagerContainerLayout.addWidget(self.cleanManagerContainer)

		self.cleanManagerLayout = QtWidgets.QVBoxLayout(self.cleanManagerContainer.contentWidget)
		# self.cleanManagerLayout.setContentsMargins(0,0,0,0)

		self.cleanActionLayout = QtWidgets.QVBoxLayout()
		self.cleanActionLayout.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
		# self.cleanActionLayout.setContentsMargins(0,0,0,0)

		self.cleanScrollAreaContentsLayout = QtWidgets.QVBoxLayout()
		

		self.cleanButtonLayout = QtWidgets.QHBoxLayout(self.deleteGroupBox)
		# self.cleanButtonLayout.setContentsMargins(0,0,0,0)

		self.cleanButtonLayout.addWidget(self.deleteUnusedButton)
		self.cleanButtonLayout.addWidget(self.deleteEmptySetsButton)
		self.cleanButtonLayout.addWidget(self.deleteEmptyLayerButton)

		cleanButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.cleanButtonLayout.addItem(cleanButtonSpacer)
		

		# self.cleanScrollAreaContentsLayout.addLayout(self.cleanButtonLayout)
		self.cleanScrollAreaContentsLayout.addWidget(self.deleteGroupBox)
		# spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
		# self.cleanScrollAreaContentsLayout.addItem(spacerItem3)

		self.cleanManagerLayout.addLayout(self.cleanScrollAreaContentsLayout)
		self.cleanManagerLayout.addLayout(self.cleanActionLayout)
		self.cleanManagerContainerLayout.addLayout(self.cleanManagerLayout)
		self.setLayout(self.cleanManagerContainerLayout)
		
	def create_connection(self):
		self.deleteUnusedButton.clicked.connect(self.DeleteUnused)
		self.deleteEmptySetsButton.clicked.connect(self.DeleteEmptySets)
		self.deleteEmptyLayerButton.clicked.connect(self.DeleteEmptyLayer)

	def DeleteUnused(self):
		
		pm.mel.MLdeleteUnused()
		print("Unused node cleaned")

	def DeleteEmptySets(self):
		
			selection_sets_list = pm.ls(type="objectSet")
			# print("Sets = ", selection_sets_list)
			for sets in selection_sets_list:
				try :
					number_of_elements = pm.sets(sets, query=True, size=True)
					if number_of_elements == 0:
						print("deleted set",sets)
						pm.delete(sets)
				except Exception as e :
					print("this set cannot be deleted because : ", e)
					continue


	def DeleteEmptyLayer(self):
		layer_list = pm.ls( type='displayLayer')
		print(layer_list)
		for lyr in layer_list:
				child_number=pm.editDisplayLayerMembers(lyr ,query=True )
				print(child_number)	
				if child_number is None :
					try :
						pm.delete(lyr)
					except Exception as e:
						print("this layer cannot be deleted because : ", e)
						continue
import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from MeshScooter.ui import main
from MeshScooter.widgets import customWidget

from maya import OpenMayaUI as omui
import os
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance

try:
    from PySide2.QtGui import QIcon
    from PySide2.QtWidgets import QWidget
except ImportError:
    from PySide.QtGui import QIcon, QWidget
			
def getMayaWindow():
	"""
	JUST ADD ME DON'T EDIT
	Get the main Maya window as a QtGui.QMainWindow instance
	@return: QtGui.QMainWindow instance of the top level Maya windows
	"""
	ptr = omui.MQtUtil.mainWindow()
	if ptr is not None:
		
		return wrapInstance(int(ptr), QtWidgets.QWidget)
	

def run():
	""" JUST ADD ME DON'T EDIT
	parents your GUI to the main maya window and displays it.

	"""
	global win
	win = main.MeshScooter(parent=getMayaWindow())
	min_width = 850
	win.setMinimumWidth(min_width)
	win_width = 650  # Set the desired width here
	win.resize(win_width, win.height())


	win.show(dockable = True)

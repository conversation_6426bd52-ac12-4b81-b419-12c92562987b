import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from MeshScooter.widgets.customWidget import *
import maya.mel as mel


class organize(QtWidgets.QDialog):

	def __init__(self, parent=None):
		super(organize, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connections()


	def create_widgets(self):
		self.OrganizeContainer = Container("Organize")

		self.CreateFolderButton = IconButton("Create Folder","+.png")
		self.FolderNameLine = LineEditWidget()




	def create_layout(self):

		self.OrganizeContainerLayout = QtWidgets.QVBoxLayout()	
		self.OrganizeContainerLayout.addWidget(self.OrganizeContainer)
		self.OrganizeContainerLayout.setContentsMargins(20,5,11,5)

		self.OrganizeLayout = QtWidgets.QVBoxLayout(self.OrganizeContainer.contentWidget)

		self.OrganizeNameLayout = QtWidgets.QHBoxLayout()
		self.OrganizeNameLayout.addWidget(self.FolderNameLine)
		OrganizeNameSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.OrganizeNameLayout.addItem(OrganizeNameSpacer)

		self.OrganizeButtonLayout = QtWidgets.QHBoxLayout()
		self.OrganizeButtonLayout.addWidget(self.CreateFolderButton)
		OrganizeButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.OrganizeButtonLayout.addItem(OrganizeButtonSpacer)

		self.OrganizeLayout.addLayout(self.OrganizeButtonLayout)
		self.OrganizeLayout.addLayout(self.OrganizeButtonLayout)
		self.OrganizeLayout.addWidget(SeparatorWidget())

		self.OrganizeContainerLayout.addLayout(self.OrganizeLayout)
		self.setLayout(self.OrganizeContainerLayout)

	def create_connections(self,):
		self.CreateFolderButton.clicked.connect(self.create_organization_folder)

	def create_organization_folder(self):
		# Create the main Asset group
		asset_group = cmds.group(empty=True, name="Asset")

		# Create 16 numbered groups (00-15) under the Asset group
		for i in range(16):
			group_name = f"ID_{i:02d}"  # Format as 2-digit number with leading zero
			numbered_group = cmds.group(empty=True, name=group_name)
			# Rename to ensure the correct name is applied
			cmds.rename(numbered_group, group_name)
			cmds.parent(group_name, asset_group)

		print(f"Created Asset group with 16 numbered subgroups (00-15)")
		return asset_group
import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from MeshScooter.widgets.customWidget import *

from MeshScooter.ui.organize import *


from maya import OpenMayaUI as omui
import os
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

from PySide2 import QtWidgets,QtCore,QtGui

class MeshScooter(MayaQWidgetDockableMixin, QtWidgets.QDialog):
	def __init__(self, parent=None):
		toolName = 'MeshScooter'
		super(MeshScooter, self).__init__(parent)
		
		self.ui = None

		self.create_widgets()
		self.create_layout()

		self.setWindowTitle(toolName)
		# self.setMinimumSize(1000,555)
		# self.setFixedWidth(1000)
	# def dockCloseEventTriggered(self):


	def create_widgets(self):
			
		# self.mainTab = CustomTabWidget()

		self.skinManagerContainer = Container("Skin Manager")
		


	def create_layout(self):
		# Main
		self.mainLayout = QtWidgets.QHBoxLayout()
		self.managerLayout = QtWidgets.QVBoxLayout()
		self.managerLayout.setContentsMargins(0,0,0,0)

		# self.managerLayout.addWidget(self.skinManagerContainer)

		

		organizeContainer = organize()


		SkinTabGroupBox = ScrollArea()

		SkinTabGroupBox.addWidget(self.skinManagerContainer)
		self.SkinContainerLayout = QtWidgets.QVBoxLayout(self.skinManagerContainer.contentWidget)
		self.SkinContainerLayout.setContentsMargins(0,0,0,0)
		self.SkinContainerLayout.addWidget(organizeContainer)

		



		creationTabVSpacer = QtWidgets.QSpacerItem(0,0, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		SkinTabGroupBox.addItem(creationTabVSpacer)

		# self.mainTab.addTab(SkinTabGroupBox, "Scene")
		
		# self.managerLayout.addWidget(self.mainTab)
		self.managerLayout.setAlignment(QtCore.Qt.AlignTop)
		
		self.mainLayout.addWidget(SkinTabGroupBox)
		self.mainLayout.addLayout(self.managerLayout)
		

		self.setLayout(self.mainLayout)
		

# import pymel.core as pm
# import maya.cmds as cmds
# from PySide2.QtCore import *
# from PySide2.QtGui import *
# from PySide2.QtWidgets import *
# from MeshScooter.widgets.customWidget import *

# from MeshScooter.ui.cleanerManager import *
# from MeshScooter.ui.skinManager import *



# from maya import OpenMayaUI as omui
# import os
# from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

# from PySide2 import QtWidgets,QtCore,QtGui
# from shiboken2 import wrapInstance

# grey_black = "rgb(45,45,45)"
# grey_dark = "rgb(65,65,65)"
# grey_light = "rgb(85,85,85)"
# grey_clear = "rgb(130,130,130)"
# orange_light = "#db9456"
# orange_dark = "#816146"
# line_height = 45
# red_warning = "rgb(125, 60, 60)"

			
# def getMayaWindow():
# 	"""
# 	JUST ADD ME DON'T EDIT
# 	Get the main Maya window as a QtGui.QMainWindow instance
# 	@return: QtGui.QMainWindow instance of the top level Maya windows
# 	"""
# 	# ptr = omui.MQtUtil.mainWindow()
# 	# mayaWindow = wrapInstance(int(ptr), QtWidgets.QWidget)
# 	# scriptJobWindow = QtWidgets.QMainWindow(parent=getMayaWindow())

# 	# windowName = 'Manager'
# 	# scriptJobWindow.setObjectName(windowName) 
# 	# scriptJobWindow.setWindowTitle(windowName)

# 	# return mayaWindow, windowName

# 	ptr = omui.MQtUtil.mainWindow()
# 	if ptr is not None:
		
# 		return wrapInstance(int(ptr), QtWidgets.QWidget)

# def run():
# 	""" JUST ADD ME DON'T EDIT
# 	parents your GUI to the main maya window and displays it.

# 	"""
# 	global win
# 	win = MeshScooter(parent=getMayaWindow())
# 	win.show(dockable = True)

# class MeshScooter(MayaQWidgetDockableMixin, QtWidgets.QDialog):
	
# 	def __init__(self, parent=None):
		


# 		toolName = 'Manager'
# 		super(MeshScooter, self).__init__(parent)
		
# 		self.ui = None

# 		self.create_widgets()
# 		self.create_layout()

# 		self.setWindowTitle(toolName)
		


# 	def create_widgets(self):

# 		self.tabManager = CustomTabWidget()

# 	def create_layout(self):
# 		self.mainLayout = QtWidgets.QHBoxLayout()

# 		self.managerLayout = QtWidgets.QVBoxLayout()
# 		self.managerLayout.setContentsMargins(0,0,0,0)

# 		Cleaner_Manager = CleanerManager()
# 		self.managerLayout.addWidget(Cleaner_Manager)


# 		skinManager = SkinManager()
# 		skinManagerGroupBox = ScrollArea()

# 		skinManagerGroupBox.addWidget(skinManager)
# 		self.tabManager.addTab(skinManagerGroupBox, "Skin")

		
# 		self.managerLayout.addWidget(self.tabManager)
# 		self.managerLayout.setAlignment(QtCore.Qt.AlignTop)
		
# 		self.mainLayout.addLayout(self.managerLayout)
		

# 		self.setLayout(self.mainLayout)

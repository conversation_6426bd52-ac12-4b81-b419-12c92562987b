import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from PrincipalSkinner.widgets.customWidget import *
import maya.mel as mel


grey_black = "rgb(45,45,45)"
grey_dark = "rgb(65,65,65)"
grey_light = "rgb(85,85,85)"
grey_clear = "rgb(130,130,130)"
orange_light = "#db9456"
orange_dark = "#816146"
line_height = 45
red_warning = "rgb(125, 60, 60)"


class selections(QtWidgets.QDialog):
	def __init__(self, parent=None):
		super(selections, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connections()


	def create_widgets(self):
		self.selectionsContainer = Container("Selection")

		self.skinDuplicateKeepButton = IconButton("Duplicate Keep Skin","+.png")

		self.skinButtonSeparator_01 = SeparatorWidget(Height=35,Width=6)

		self.selectHierarchyButton = IconButton("Select Hierarchy","+.png")
		self.selectChainButton = IconButton("Select Chain","+.png")



	def create_layout(self):

		self.selectionsContainerLayout = QtWidgets.QVBoxLayout()	
		self.selectionsContainerLayout.addWidget(self.selectionsContainer)
		self.selectionsContainerLayout.setContentsMargins(20,5,11,5)

		self.selectionssLayout = QtWidgets.QVBoxLayout(self.selectionsContainer.contentWidget)

		self.selectionsButtonLayout = QtWidgets.QHBoxLayout()
		self.selectionsButtonLayout.addWidget(self.skinDuplicateKeepButton)
		self.selectionsButtonLayout.addWidget(self.selectHierarchyButton)
		self.selectionsButtonLayout.addWidget(self.selectChainButton)
		selectionsButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.selectionsButtonLayout.addItem(selectionsButtonSpacer)

		self.selectionssLayout.addLayout(self.selectionsButtonLayout)
		self.selectionssLayout.addWidget(SeparatorWidget())

		self.selectionsContainerLayout.addLayout(self.selectionssLayout)
		self.setLayout(self.selectionsContainerLayout)

	def create_connections(self,):
		self.skinDuplicateKeepButton.clicked.connect(self.duplicate_keep_skin_selection)
		self.selectHierarchyButton.clicked.connect(self.select_hierarchy)


	def duplicate_keep_skin_selection(self) :
		selection = pm.ls(selection = True)
		for sel in selection :
			self.duplicate_and_keep_skin_cluster(sel)

	def select_hierarchy(self):
		selection = pm.ls(selection = True)
		pm.select(selection, hierarchy=True)

	def duplicate_and_keep_skin_cluster(self,original_mesh, ):

		new_mesh_name = original_mesh +"_02"
		duplicated_mesh = pm.duplicate(original_mesh, name=new_mesh_name)
		original_skin_cluster = self.get_skin_cluster(original_mesh)
		print("original_skin_cluster = ", original_skin_cluster)
		
		joints = pm.skinCluster(original_skin_cluster, query=True, influence=True)
		duplicated_skin_cluster = pm.skinCluster(joints, duplicated_mesh, bindMethod=0, toSelectedBones=True, normalizeWeights=1)

		print("duplicated_skin_cluster = ", duplicated_skin_cluster)
		pm.copySkinWeights(sourceSkin=original_skin_cluster, destinationSkin=duplicated_skin_cluster, noMirror=True)

		return duplicated_mesh

	def get_skin_cluster(self,mesh):
		# Get the shape node of the mesh
		mesh_shape = pm.listRelatives(mesh, shapes=True)

		# Get the skin cluster associated with the mesh's shape
		skin_cluster = pm.listConnections(mesh_shape, type="skinCluster")
		
		if skin_cluster:
			return skin_cluster[0]
		else:
			return None
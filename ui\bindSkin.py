import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from PrincipalSkinner.widgets.customWidget import *
import maya.mel as mel


grey_black = "rgb(45,45,45)"
grey_dark = "rgb(65,65,65)"
grey_light = "rgb(85,85,85)"
grey_clear = "rgb(130,130,130)"
orange_light = "#db9456"
orange_dark = "#816146"
line_height = 45
red_warning = "rgb(125, 60, 60)"


class bindSkin(QtWidgets.QDialog):
	def __init__(self, parent=None):
		super(bindSkin, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		self.create_connections()


	def create_widgets(self):
		self.selectionsContainer = Container("Binding")

		self.BindButton = IconButton("Bind Skin","+.png")

		# self.skinButtonSeparator_01 = SeparatorWidget(Height=35,Width=6)
		bindToComboBoxElements = ["Joint Hierarchy", "Selected Joints"]
		self.bindToComboBox = ComboBox(combo_elements_names = bindToComboBoxElements)

		self.MaxInfluencesLabel = LabelTagWidget("Max Influences : ")
		self.MaxInfluencesSpin = spinBoxWidget(default_value=3, min_max=(1, 6), step=1, suffix="")

		self.mirroSkinButton = IconButton("Mirror Skin","+.png")
		mirrorSkinRadioNames = ["XY", "YZ", "XZ"]
		self.mirrorSkinRadioGroup = RadioButtonWidget("YZ", mirrorSkinRadioNames)
		self.mirrorSkinPosToNegCheckBox = CheckBoxWidget(name="Positive   \u25B6   Negative")

		self.addInfluenceButton = IconButton("Add Influence","+.png")

		self.RemoveInfluenceButton = IconButton("Remove Influence","+.png")





	def create_layout(self):

		self.selectionsContainerLayout = QtWidgets.QVBoxLayout()	
		self.selectionsContainerLayout.addWidget(self.selectionsContainer)
		self.selectionsContainerLayout.setContentsMargins(20,5,11,5)

		self.selectionsLayout = QtWidgets.QVBoxLayout(self.selectionsContainer.contentWidget)

		self.selectionsButtonLayout = QtWidgets.QHBoxLayout()
		self.selectionsButtonLayout.addWidget(self.BindButton)
		self.selectionsButtonLayout.addWidget(self.bindToComboBox)
		self.selectionsButtonLayout.addWidget(self.MaxInfluencesLabel)
		self.selectionsButtonLayout.addWidget(self.MaxInfluencesSpin)
		selectionsButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.selectionsButtonLayout.addItem(selectionsButtonSpacer)

		self.mirrorSkinButtonLayout = QtWidgets.QHBoxLayout()

		self.mirrorSkinButtonLayout.addWidget(self.mirroSkinButton)
		self.mirrorSkinButtonLayout.addWidget(self.mirrorSkinRadioGroup)
		self.mirrorSkinButtonLayout.addWidget(self.mirrorSkinPosToNegCheckBox)

		selectionsButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.mirrorSkinButtonLayout.addItem(selectionsButtonSpacer)

		self.editInfluenceButtonLayout = QtWidgets.QHBoxLayout()

		self.editInfluenceButtonLayout.addWidget(self.addInfluenceButton)
		self.editInfluenceButtonLayout.addWidget(self.RemoveInfluenceButton)


		selectionsButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.editInfluenceButtonLayout.addItem(selectionsButtonSpacer)

		
		self.selectionsLayout.addLayout(self.selectionsButtonLayout)
		self.selectionsLayout.addWidget(SeparatorWidget())
		self.selectionsLayout.addLayout(self.mirrorSkinButtonLayout)
		self.selectionsLayout.addWidget(SeparatorWidget())
		self.selectionsLayout.addLayout(self.editInfluenceButtonLayout)
		self.selectionsLayout.addWidget(SeparatorWidget())

		self.selectionsContainerLayout.addLayout(self.selectionsLayout)
		self.setLayout(self.selectionsContainerLayout)

	def create_connections(self,):
		self.BindButton.clicked.connect(self.skinculster_meshes)
		self.mirroSkinButton.clicked.connect(self.mirror_skin_weights)
		self.addInfluenceButton.clicked.connect(self.add_influence)
		self.RemoveInfluenceButton.clicked.connect(self.remove_influence)

	def add_influence(self):
		selected = cmds.ls(selection=True)
		selected_joints,selected_mesh = self.get_selected_mesh_adn_joints(selected)
		for mesh in selected_mesh:
			skinCluster=self.get_skin_cluster(mesh)
			cmds.skinCluster(str(skinCluster), edit=True, addInfluence=selected_joints, weight=0.0,lockWeights=True)
	def remove_influence(self):
		selected = cmds.ls(selection=True)
		selected_joints,selected_mesh = self.get_selected_mesh_adn_joints(selected)
		for mesh in selected_mesh:
			skinCluster=self.get_skin_cluster(mesh)
			cmds.skinCluster(str(skinCluster), edit=True, removeInfluence=selected_joints)


	def get_selected_mesh_adn_joints(self,selected):
		selected_joint = cmds.ls(selected, shapes=False, type="joint")
		selected_mesh = [item for item in selected if item not in selected_joint]

		return selected_joint,selected_mesh

	def get_skin_cluster(self,mesh):
		# Get the shape node of the mesh
		mesh_shape = pm.listRelatives(mesh, shapes=True)

		# Get the skin cluster associated with the mesh's shape
		skin_cluster = pm.listConnections(mesh_shape, type="skinCluster")
		
		if skin_cluster:
			return skin_cluster[0]
		else:
			return None

	def mirror_skin_weights(self):
		
		selected = cmds.ls(selection=True)
		mirorr_plane_selection = self.mirrorSkinRadioGroup.button_group.checkedButton()
		print("MIRROR MODE" , mirorr_plane_selection.text())
		if self.mirrorSkinPosToNegCheckBox.checkBox.checkState():
			mirror_skin_direction = False
		else:
			mirror_skin_direction = True
		for mesh in selected:
			currentSkinCluster = self.get_skin_cluster(mesh)
			cmds.copySkinWeights( ss=str(currentSkinCluster), ds=str(currentSkinCluster), mirrorMode=mirorr_plane_selection.text(), mirrorInverse=mirror_skin_direction,normalize=True, )

	def skinculster_meshes(self):

		selected = cmds.ls(selection=True)
		selected_joint,selected_mesh = self.get_selected_mesh_adn_joints(selected)
		joint_max_influence = self.MaxInfluencesSpin.value()
		selected_option = self.bindToComboBox.currentIndex()
		self.skinCluster = []
		root_joint = cmds.listRelatives(selected_joint, parent=True, fullPath=True)
		root_joint = root_joint[0].split('|')[1]
		joint_list= cmds.listRelatives(root_joint, children=True, allDescendents=True)

		if selected_option == 0:
			joint_to_skin = joint_list
		elif selected_option == 1:
			joint_to_skin = selected_joint

		for mesh in selected_mesh:
			skin_clusters = cmds.ls(cmds.listHistory(mesh), type="skinCluster")
			if not skin_clusters:
				sk_skin = cmds.skinCluster(joint_to_skin, mesh,tsb=True, maximumInfluences=joint_max_influence, bindMethod=0,skinMethod=0,normalizeWeights=1,weightDistribution=0)
				self.skinCluster.append(sk_skin)

		print("JOINTS = ", joint_to_skin, "MESHES = ",selected_mesh )



	def duplicate_keep_skin_selection(self) :
		selection = pm.ls(selection = True)
		for sel in selection :
			self.duplicate_and_keep_skin_cluster(sel)

	def select_hierarchy(self):
		selection = pm.ls(selection = True)
		pm.select(selection, hierarchy=True)

	def duplicate_and_keep_skin_cluster(self,original_mesh, ):

		new_mesh_name = original_mesh +"_02"
		duplicated_mesh = pm.duplicate(original_mesh, name=new_mesh_name)
		original_skin_cluster = self.get_skin_cluster(original_mesh)
		print("original_skin_cluster = ", original_skin_cluster)
		
		joints = pm.skinCluster(original_skin_cluster, query=True, influence=True)
		duplicated_skin_cluster = pm.skinCluster(joints, duplicated_mesh, bindMethod=0, toSelectedBones=True, normalizeWeights=1)

		print("duplicated_skin_cluster = ", duplicated_skin_cluster)
		pm.copySkinWeights(sourceSkin=original_skin_cluster, destinationSkin=duplicated_skin_cluster, noMirror=True)

		return duplicated_mesh

	def get_skin_cluster(self,mesh):
		# Get the shape node of the mesh
		mesh_shape = pm.listRelatives(mesh, shapes=True)

		# Get the skin cluster associated with the mesh's shape
		skin_cluster = pm.listConnections(mesh_shape, type="skinCluster")
		
		if skin_cluster:
			return skin_cluster[0]
		else:
			return None
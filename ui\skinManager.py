import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import maya.OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
from PrincipalSkinner.widgets.customWidget import *
import maya.mel as mel


grey_black = "rgb(45,45,45)"
grey_dark = "rgb(65,65,65)"
grey_light = "rgb(93,93,93)"
grey_clear = "rgb(130,130,130)"
orange_light = "#cc42ca"
orange_dark = "#6d4681"
line_height = 45
red_warning = "rgb(125, 60, 60)"


class SkinManager(QtWidgets.QDialog):
	def __init__(self, parent=None):
		super(SkinManager, self).__init__(parent=None)
		self.ui = None

		self.create_widgets()
		self.create_layout()
		# self.create_connections()


	def create_widgets(self):
		# Manager Reference
		self.skinManagerContainer = Container("Skin Manager")

		self.toolContainer = Container("Tool")
		self.selectContainer = Container("Selection")
		self.copySkinContainer = Container("Copy Skin")

		# self.skinMeshGroupbox = customGroupBox(Text="Mesh")
		# # self.skinMeshGroupbox.setStyleSheet("QGroupBox { padding: 0px; }")
		# self.skinSelectGroupbox = customGroupBox(Text="Select")
		# self.skinCopyGroupbox = customGroupBox(Text="Copy")
		# self.skinEditGroupbox = customGroupBox(Text="Edit")

		# self.toolGroupbox = customFrame(Text="Tool")
		# self.skinMeshGroupbox = customFrame(Text="Mesh")
		# self.skinSelectGroupbox = customFrame(Text="Select",)
		# self.skinCopyGroupbox = customFrame(Text="Copy",)
		# self.skinEditGroupbox = customFrame(Text="Edit")

		# self.toolGroupbox = customFrame()
		# self.skinMeshGroupbox = customFrame()
		# self.skinSelectGroupbox = customFrame()
		# self.skinCopyGroupbox = customFrame()
		# self.skinEditGroupbox = customFrame()

		self.paintToolPress = SquareButton("Paint", size = 75, contourColor=grey_light)
		self.paintSmoothToolPress = SquareButton("Smooth", size = 75, contourColor=grey_light)
		self.componentEditorPress = SquareButton("Component", size = 75, contourColor=grey_light)


		self.copyListGroupBox = customFrame(color=grey_dark,)
		self.sourceList = addSubtractList("Source")
		self.listArrow = QtWidgets.QLabel(">")
		self.targetList = addSubtractList("Target")
		self.copySkinListButton = IconButton("Copy source to target","+.png")


		copySkinRadioNames = ["All sources to 1 target", "Group to Group", "One to One"]
		self.copySkinRadioGroup = RadioButtonWidget("All sources to 1 target", copySkinRadioNames)

		# self.copySkinListAllto1Radio = QtWidgets.QRadioButton("All sources to 1 target")
		# self.copySkinListGroupModeRadio = QtWidgets.QRadioButton("Group to Group")
		# self.copySkinListOneToOneRadio = QtWidgets.QRadioButton("One to One")

		# self.skinCreateSeparator_01 = SeparatorWidget(Height=4)

		self.skinDuplicateKeepButton = IconButton("Duplicate Keep Skin","+.png")

		self.skinButtonSeparator_01 = SeparatorWidget(Height=35,Width=6)

		self.selectHierarchyButton = IconButton("Select Hierarchy","+.png")
		self.selectChainButton = IconButton("Select Chain","+.png")

		self.copyWeightButton = IconButton("Copy Weight","+.png")


	def create_layout(self):
		# Manager skin

		# self.skinManagerContainerLayout = QtWidgets.QVBoxLayout()	
		# self.skinManagerContainerLayout.addWidget(self.skinManagerContainer)
		# self.skinManagerContainerLayout.setContentsMargins(20,5,11,5)

		# self.skinManagerLayout = QtWidgets.QVBoxLayout(self.skinManagerContainer.contentWidget)
		self.mainLayout = QtWidgets.QHBoxLayout()
		self.managerLayout = QtWidgets.QVBoxLayout()
		self.managerLayout.setContentsMargins(0,0,0,0)
		skinManagerTabGroupBox = ScrollArea()

		skinManagerTabGroupBox.addWidget(self.skinManagerContainer)
		self.skinManagerContainerLayout = QtWidgets.QVBoxLayout(self.skinManagerContainer.contentWidget)
		self.skinManagerContainerLayout.setContentsMargins(0,0,0,0)
		self.skinManagerContainerLayout.addWidget(self.toolContainer)
		self.skinManagerContainerLayout.addWidget(self.selectContainer)
		self.skinManagerContainerLayout.addWidget(self.copySkinContainer)


		# self.toolContainerLayout = QtWidgets.QVBoxLayout()	
		# self.toolContainerLayout.addWidget(self.toolContainer)
		# self.toolContainerLayout.setContentsMargins(20,5,11,5)

		# self.toolLayout = QtWidgets.QVBoxLayout(self.toolContainer.contentWidget)

		# self.toolButtonLayout = QtWidgets.QHBoxLayout()
		# self.toolButtonLayout.addWidget(self.paintToolPress)
		# self.toolButtonLayout.addWidget(self.paintSmoothToolPress)
		# self.toolButtonLayout.addWidget(self.componentEditorPress)
		# toolButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		# self.toolButtonLayout.addItem(toolButtonSpacer)
		

		###################
		self.skinCopyGroupboxVLayout = QtWidgets.QVBoxLayout(self.skinCopyGroupbox)
		self.skinCopyGroupboxVLayout.setAlignment(QtCore.Qt.AlignTop)

		self.skinCopyGroupboxHLayout_01 = QtWidgets.QHBoxLayout()
		self.skinCopyGroupboxHLayout_01.setAlignment(QtCore.Qt.AlignTop)			
		self.skinCopyGroupboxVLayout.addLayout(self.skinCopyGroupboxHLayout_01)
		self.skinCopyGroupboxVLayout.addWidget(SeparatorWidget())
		
		self.skinCopyGroupboxHLayout_01.addWidget(self.copyWeightButton)
		

		self.skinCopyGroupboxVLayout.addWidget(self.copyListGroupBox)

		self.skinCopyListVLayout_01 = QtWidgets.QVBoxLayout(self.copyListGroupBox)
		self.skinCopyListVLayout_01.setAlignment(QtCore.Qt.AlignTop)

		self.skinCopyListHLayout_01 = QtWidgets.QHBoxLayout()
		self.skinCopyListHLayout_01.setAlignment(QtCore.Qt.AlignTop)	
		self.skinCopyListVLayout_01.addLayout(self.skinCopyListHLayout_01)

		self.skinCopyListHLayout_01.addWidget(self.sourceList)
		self.skinCopyListHLayout_01.addWidget(self.listArrow)
		self.skinCopyListHLayout_01.addWidget(self.targetList)
		
		
		self.skinCopyListHLayout_02 = QtWidgets.QHBoxLayout()
		self.skinCopyListHLayout_02.setAlignment(QtCore.Qt.AlignTop)
		self.skinCopyListVLayout_01.addLayout(self.skinCopyListHLayout_02)
		

		self.skinCopyListHLayout_02.addWidget(self.copySkinListButton)
		self.skinCopyListHLayout_02.addWidget(self.copySkinRadioGroup)
		# self.skinCopyListHLayout_02.addWidget(self.copySkinListGroupModeRadio)
		# self.skinCopyListHLayout_02.addWidget(self.copySkinListOneToOneRadio)
		

		

		shrinkButtonSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.skinCopyListHLayout_02.addItem(shrinkButtonSpacer)

		copy01Spacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
		self.skinCopyGroupboxHLayout_01.addItem(copy01Spacer)

		copyVSpacer = QtWidgets.QSpacerItem(0, 0, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		self.skinCopyGroupboxVLayout.addItem(copyVSpacer)

		###################
		self.skinEditGroupboxVLayout = QtWidgets.QVBoxLayout(self.skinEditGroupbox)

		# self.skinManagerLayout.addLayout(self.skinButtonLayout)

		self.skinManagerLayout.addLayout(self.skinManagerVLayout)
		skinManagerSpacer = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		self.skinManagerVLayout.addItem(skinManagerSpacer)

		self.managerLayout.setAlignment(QtCore.Qt.AlignTop)
		
		self.mainLayout.addWidget(skinManagerTabGroupBox)
		self.mainLayout.addLayout(self.managerLayout)
		

		self.setLayout(self.mainLayout)

	def create_connections(self,):

		self.paintToolPress.clicked.connect(self.open_paint_skin_weights_tool)
		self.paintSmoothToolPress.clicked.connect(self.open_paint_skin_smooth_tool)
		self.componentEditorPress.clicked.connect(self.open_component_editor)

		self.skinDuplicateKeepButton.clicked.connect(self.duplicate_keep_skin_selection)
		self.selectHierarchyButton.clicked.connect(self.select_hierarchy)

		self.copyWeightButton.clicked.connect(self.copy_skin_selection)

		self.sourceList.addButton.clicked.connect(lambda: self.add_list_item(self.sourceList))
		self.sourceList.subtractButton.clicked.connect(lambda: self.remove_selected_item(self.sourceList))
		self.sourceList.listWidget.itemDoubleClicked.connect(lambda item=self.sourceList.listWidget.currentItem(): self.clear_selection(self.sourceList, item))
		self.sourceList.clearButton.clicked.connect(lambda: self.clear_list(self.sourceList))

		self.targetList.addButton.clicked.connect(lambda: self.add_list_item(self.targetList))
		self.targetList.subtractButton.clicked.connect(lambda: self.remove_selected_item(self.targetList))
		self.targetList.listWidget.itemDoubleClicked.connect(lambda item=self.targetList.listWidget.currentItem(): self.clear_selection(self.targetList, item))
		self.targetList.clearButton.clicked.connect(lambda: self.clear_list(self.targetList))

		self.copySkinListButton.clicked.connect(self.copy_skin_list)


	def open_paint_skin_weights_tool(self):
		# if we're not currently in the paint skin weights tool context, get us into it
		current_context = cmds.currentCtx()
			
		# If we are already in the Paint Skin Weights Tool, exit the tool
		if current_context == "artAttrSkinContext":
			cmds.setToolTo("selectSuperContext")
		else:
			# If not, switch to the Paint Skin Weights Tool
			mel.eval("ArtPaintSkinWeightsTool;")

	def open_paint_skin_smooth_tool(self):
		# if we're not currently in the paint skin weights tool context, get us into it
		current_context = cmds.currentCtx()
			
		# If we are already in the Paint Skin Weights Tool, exit the tool
		if current_context == "brSmoothWeightsContext1":
			cmds.setToolTo("selectSuperContext")
		else:
			# If not, switch to the Paint Skin Weights Tool
			mel.eval("brSmoothWeightsToolCtx;")

	def open_component_editor(self):
		mel.eval('componentEditorWindow()')
		cmds.componentEditor( 'componentEditorWinComponEditor', q=True, ctl=True )	

	def copy_skin_list(self) :
		source_meshes = self.get_all_item_in_list(self.sourceList)
		print("source_meshes", source_meshes)
		target_meshes = self.get_all_item_in_list(self.targetList)
		print("target_meshes", target_meshes)

		copySkin_selected_option = self.copySkinRadioGroup.button_group.checkedButton()
		# self.sk_skinCluster = []
		if selected_option.text() == "All sources to 1 target":
			self.copy_allsources_to_onetarget(source_meshes, target_meshes)

		# if self.copySkinListAllto1Radio.isChecked():
		# 	self.copy_allsources_to_onetarget(source_meshes, target_meshes)
		# elif self.copySkinListGroupModeRadio.isChecked():


		# elif self.copySkinListOneToOneRadio.isChecked():


	# def copy_source_to_target_OneToOne(self,source_meshes, target_meshes ):
	# 	pass

	# def copy_source_group_to_target_group(self,source_groups, target_groups ):
	# 	for source_group in source_groups:




	def copy_allsources_to_onetarget(self,source_meshes, target_meshes ):
		source_skinned_mesh = []
		for source in source_meshes :
			source_skinned_mesh.append(self.duplicate_and_keep_skin_cluster(source))

		unified_source_mesh = pm.polyUniteSkinned(source_skinned_mesh, ch=0)
		pm.delete(source_skinned_mesh)

		for target in target_meshes :
			self.copy_skin_Weight(unified_source_mesh,target )

		pm.delete(unified_source_mesh)


	def get_all_item_in_list(self, List):
		list_count = List.listWidget.count()
		list_meshes = []
		for index in range(list_count):
			# Get the item at the current index
			item = List.listWidget.item(index)
			# Get the text of each item
			mesh_name = item.text()
			list_meshes.append(mesh_name)
		
		return list_meshes
	def clear_selection(self, List, item,):
		print("cleared")
		# print(", iindex", index)
		if item.isSelected(): # Check if the item is not selected
			List.listWidget.clearSelection()  # Clear the selection
	def clear_list(self, List):
		print("cleared")
		List.listWidget.clear()  # Clear the selection

	def add_list_item(self, List):
		selection = pm.ls(selection = True,long=True, )
		for obj in selection:
			if pm.objectType(obj) == 'transform':
				if not self.object_in_list(List.listWidget, obj):
					item = QtWidgets.QListWidgetItem(str(obj))
					List.add_list_item(item)

	def object_in_list(self, listWidget, obj_name):
		# Iterate through existing items in the list and check if the object name matches
		for row in range(listWidget.count()):
			existing_item = listWidget.item(row)
			if existing_item.text() == obj_name:
				return True, existing_item
		return False

	def remove_selected_item(self, List):
		selected_item = List.listWidget.currentItem()
		selection = pm.ls(selection = True,long=True)
		try :
			if selected_item.isSelected():
				print("selected_item", selected_item)
				List.listWidget.takeItem(List.listWidget.row(selected_item))
			else :
				if selection:
					for obj in selection:
						exist, existing_item = self.object_in_list(List.listWidget, obj)
						if exist:
							print("OBJ IN LST ! ", existing_item)
							List.listWidget.takeItem(List.listWidget.row(existing_item))
		except Exception as e:
			try :
				if selection:
					for obj in selection:
						exist, existing_item = self.object_in_list(List.listWidget, obj)
						if exist:
							List.listWidget.takeItem(List.listWidget.row(existing_item))
			except Exception as e:
				pass

	

	def duplicate_keep_skin_selection(self) :
		selection = pm.ls(selection = True)
		for sel in selection :
			self.duplicate_and_keep_skin_cluster(sel)

	# def duplicate_Keep_Skin(self, original_mesh, ):
	# 	new_mesh_name = original_mesh +"_02"
	# 	duplicated_mesh = pm.duplicate(original_mesh, name=new_mesh_name)
	# 	original_mesh_shape = pm.listRelatives(original_mesh, shapes=True)

	# 	skin_cluster = pm.listConnections(original_mesh_shape,type="skinCluster")
	# 	print("skin_cluster = ", skin_cluster)
	# 	if skin_cluster:
	# 		pm.skinCluster(duplicated_mesh, e=True, skinCluster=skin_cluster)

	def copy_skin_selection(self,):
		selection = pm.ls(selection = True)

		selected_meshes = selection[:-1]
		reception_mesh = selection[-1]
		source_meshes = []
		for sel in selected_meshes :
			source_meshes.append(self.duplicate_and_keep_skin_cluster(sel))
		# print("source_mesh = ", source_meshes)
		unified_source_mesh = pm.polyUniteSkinned(source_meshes, ch=0)
		pm.delete(source_meshes)
		# print("source_mesh after unify= ", unified_source_mesh)
		
		print("reception_mesh = ", reception_mesh)
		self.copy_skin_Weight(unified_source_mesh,reception_mesh )
		pm.delete(unified_source_mesh)


	def copy_skin_Weight(self, source_mesh,reception_mesh):

		original_skin_cluster = self.get_skin_cluster(source_mesh)
		reception_skin_cluster = self.get_skin_cluster(reception_mesh)
		if reception_skin_cluster is not None: 
			pm.copySkinWeights(sourceSkin=original_skin_cluster, destinationSkin=reception_skin_cluster, noMirror=True)
			print("normal copy ")
		else :
			joints = pm.skinCluster(original_skin_cluster, query=True, influence=True)
			new_reception_skin_cluster = pm.skinCluster(joints, reception_mesh, bindMethod=0, toSelectedBones=True, normalizeWeights=1)
			pm.copySkinWeights(sourceSkin=original_skin_cluster, destinationSkin=new_reception_skin_cluster, noMirror=True)
			print("new_reception_skin_cluster = ",new_reception_skin_cluster)


	def select_hierarchy(self):
		selection = pm.ls(selection = True)
		pm.select(selection, hierarchy=True)

	def duplicate_and_keep_skin_cluster(self,original_mesh, ):

		new_mesh_name = original_mesh +"_02"
		duplicated_mesh = pm.duplicate(original_mesh, name=new_mesh_name)
		original_skin_cluster = self.get_skin_cluster(original_mesh)
		print("original_skin_cluster = ", original_skin_cluster)
		
		joints = pm.skinCluster(original_skin_cluster, query=True, influence=True)
		duplicated_skin_cluster = pm.skinCluster(joints, duplicated_mesh, bindMethod=0, toSelectedBones=True, normalizeWeights=1)

		print("duplicated_skin_cluster = ", duplicated_skin_cluster)
		pm.copySkinWeights(sourceSkin=original_skin_cluster, destinationSkin=duplicated_skin_cluster, noMirror=True)

		return duplicated_mesh

	def get_skin_cluster(self,mesh):
		# Get the shape node of the mesh
		mesh_shape = pm.listRelatives(mesh, shapes=True)

		# Get the skin cluster associated with the mesh's shape
		skin_cluster = pm.listConnections(mesh_shape, type="skinCluster")
		
		if skin_cluster:
			return skin_cluster[0]
		else:
			return None
import pymel.core as pm
import maya.cmds as cmds
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from maya import OpenMayaUI as omui
from PySide2 import QtWidgets,QtCore,QtGui
from shiboken2 import wrapInstance
import os



grey_black = "rgb(45,45,45)"
grey_dark = "rgb(65,65,65)"
grey_light = "rgb(93,93,93)"
grey_clear = "rgb(130,130,130)"
orange_light = "#42cc82"
orange_dark = "#22593D"
line_height = 45
red_warning = "rgb(125, 60, 60)"

def get_icon(icon_name="+.png"):
	script_directory = os.path.dirname(__file__)
	# print(script_directory)
	project_directory = os.path.dirname(script_directory)
	# print(script_directory)
	icons_directory = os.path.join(project_directory, 'icons')
	# print(project_directory)
	icon_path = os.path.join(icons_directory, icon_name)
	# print(icon_path)
	if os.path.exists(icon_path):
		print("EXIST")
	else:
		# icon_path = icon_name
		print("TOO BAD")
	return icon_path

class IconButton(QtWidgets.QPushButton):
	def __init__(self, text, icon = "+.png",parent=None, color =grey_light, w = 25+line_height,h=line_height ):
		super(IconButton, self).__init__(parent)
		
		icon_path = get_icon(icon)
		self.setText(text)
		self.setStyleSheet(f"QPushButton{{background-color: {color};}}")
		self.setMinimumSize(w,h)
		# self.setMaximumSize(150+line_height, line_height)
		# self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		# self.label = QtWidgets.QLabel() 
		# self.icon = QtGui.QIcon(icon_path)

		# pixmap = self.icon.pixmap(QtCore.QSize(line_height, line_height))

		# self.label.setPixmap(pixmap)
		# layout = QtWidgets.QHBoxLayout(self)
		# layout.setContentsMargins(0, 0, 0, 0)
		# layout.setAlignment(QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)

		# # Add a spacer to push the text to the right
		# spacer_item = QtWidgets.QSpacerItem(0, 0, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		# layout.addWidget(self.label)
		# layout.addItem(spacer_item)

		# # Set the layout for the button
		# self.setLayout(layout)

class ComboBox(QtWidgets.QComboBox):
	def __init__(self,combo_elements_names, parent=None, color =grey_light, w = 25+line_height,h=line_height ):
		super(ComboBox, self).__init__(parent)
		
		# self.setText(text)
		for i, combo_elements_names in enumerate(combo_elements_names):
			self.addItem(combo_elements_names)
		self.setStyleSheet(f"QComboBox{{background-color: {color};}}")
		self.setMinimumSize(w,h)

class spinBoxWidget(QtWidgets.QSpinBox):
	def __init__(self, default_value, min_max, step, suffix):
		super(spinBoxWidget, self).__init__()

		self.setSuffix(suffix)
		self.setRange(*min_max)
		self.setValue(default_value)
		self.setSingleStep(step)
		self.setContentsMargins(0, 0, 0, 0)
		self.setMinimumSize(250, line_height)
		self.setMaximumSize(300, line_height)
		self.setFont(basic_font(10,False))
		self.setStyleSheet(
			f"QSpinBox{{background-color: {grey_black}; border-style: none; padding-right: 2px;padding-left: 2px;}}"

			f"QSpinBox::down-button{{subcontrol-origin: padding; subcontrol-position: left; width: 45px;height: 45px;border-style: none;background-color: {grey_dark}}}"
			f"QSpinBox::up-button{{subcontrol-origin: padding; subcontrol-position: right; width: 45px;height: 45px;border-style: none;background-color: {grey_dark}}}"

			f"QSpinBox::up-arrow{{image: url( :/nodeGrapherArrowUp.png)}}"
			f"QSpinBox::down-arrow{{image: url( :/nodeGrapherArrowDown.png)}}"
			)
		
	def wheelEvent(self, event):
		event.ignore()

class SliderWidget(QSlider):
    def __init__(self, parent=None, color = grey_black):
        super(SliderWidget, self).__init__(QtCore.Qt.Horizontal)
        self.setFixedHeight(line_height)
        self.setFixedWidth(line_height*4)
        self.setMinimum(0)
        self.setMaximum(10)
        self.setTickInterval(0.1)
        # self.setPageStep(10)
        self.setTracking(True)
        # self.setFont(basic_font(12, False))
        self.setStyleSheet(
            f"QSlider{{background-color: {color}; border-style: none;padding: 6px}}"
            f"QSlider::groove:horizontal{{background-color: {grey_clear}; height: 2px;}}"
            f"QSlider::handle:horizontal{{background-color: {orange_light}; width: 8px; height: 30px;margin: -14px 0px;}}"
            )
    #     self.set_background_color(color)

class RadioButtonWidget(QtWidgets.QWidget):
	clicked = QtCore.Signal()  # Custom signal
	def __init__(self, name, radio_button_names):
		super(RadioButtonWidget, self).__init__()
		# Create the main layout
		main_layout = QtWidgets.QVBoxLayout(self)
		main_layout.setSpacing(10)
		main_layout.setContentsMargins(0, 0, 0, 0)

		# Create a container widget for the label and button
		container_widget = QtWidgets.QWidget(self)
		# container_widget.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
		container_widget.setMinimumSize(0, line_height)
		container_widget.setMaximumSize(10000, line_height)
		container_layout = QtWidgets.QHBoxLayout(container_widget)
		# container_widget.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)

		container_layout.setSpacing(25)
		container_layout.setContentsMargins(25, 5, 25, 5)

		# Create the radio button group
		self.button_group = QtWidgets.QButtonGroup()


		# Create the radio buttons and add them to the button group
		for i, radio_button_name in enumerate(radio_button_names):
			radio_button = RadioButton(radio_button_name)
			self.button_group.addButton(radio_button)
			container_layout.addWidget(radio_button)

			if radio_button_name == name:  # Check if the radio button name matches the 'name' variable
				radio_button.setChecked(True)

		# Set the background color of the container widget
		container_widget.setStyleSheet(f"QWidget{{background-color: {grey_black}; border-style: none;text-align: center;}}")

		main_layout.addWidget(container_widget)

	def emitClicked(self):
		self.clicked.emit()  # Emit the custom clicked signal

class RadioButton(QtWidgets.QRadioButton):
	def __init__(self, text, parent=None):
		super(RadioButton, self).__init__(parent)

		# Set the text and icon
		self.setText(text)
		self.setStyleSheet(f"QRadioButton::indicator:checked {{background-color: {orange_light}; border-style: none;}}"
				f"QRadioButton::indicator:hover {{background-color: {grey_light}; border-style: none;border: 0.2em solid {orange_dark}}}"
				f"QRadioButton::indicator {{background-color: {grey_light}; border-style: none;}}")
		# self.setIcon(QtGui.QIcon(icon_path))
		# self.setIconSize(QtCore.QSize(24, 24))  # Adjust the size as needed

def basic_font(size = 8, spacing = 1, bold = True):
	font = QtGui.QFont()
	font.setBold(bold)
	font.setPointSize(size)
	font.setFamily("Calibri Light")
	font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, spacing)

	return font

def header_font(size = 10, spacing = 10, bold = True):
	font = QtGui.QFont()
	font.setBold(bold)
	font.setPointSize(size)
	font.setFamily("Calibri Light")
	font.setLetterSpacing(QtGui.QFont.AbsoluteSpacing, spacing)
	return font

class deleteButton(QtWidgets.QPushButton):
	def __init__(self, parent=None, Text="X", color=orange_dark ):
		super(deleteButton, self).__init__()
		self.setText(Text)
		self.setMinimumSize(QtCore.QSize(0, 0))
		self.setMaximumSize(QtCore.QSize(32, 32))
		self.setStyleSheet(f"QPushButton {{background-color: {color};}}")

class SquareButton(QtWidgets.QPushButton):
	def __init__(self, Text="X", sizeX = 32, sizeY = 32, contourColor=None):
		super(SquareButton, self).__init__()
		self.setText(Text)
		self.setMinimumSize(QtCore.QSize(sizeX, sizeY))
		self.setMaximumSize(QtCore.QSize(sizeX, sizeY))
		if contourColor is not None:
			self.setStyleSheet(f"QPushButton {{background-color: {orange_dark};border: 3px solid {contourColor};}}")

class quickAccessButton(QtWidgets.QPushButton):
	def __init__(self, parent=None, Text="Quick", ):
		super(quickAccessButton, self).__init__()
		self.setText(Text)
		self.setMinimumSize(QtCore.QSize(0, 0))
		self.setMaximumSize(QtCore.QSize(30, 30))
		self.setStyleSheet(f"QPushButton {{background-color: {grey_clear};}}")

class customGroupBox(QtWidgets.QGroupBox):
	def __init__(self, parent=None, Text="Quick", ):
		super(customGroupBox, self).__init__()
		self.setTitle(Text)
		self.setFlat(True)
		self.setMinimumSize(1,1)
		# self.setMaximumSize(50,50)
		self.setAlignment(QtCore.Qt.AlignTop)
		# self.setHeight(1)
		# self.setmHeight(1)
		# self.setLayout(QVBoxLayout())
		# self.layout().setContentsMargins(0, 0, 0, 0)
		# self.layout().setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
		# self.sizeHint()
		self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)

		# self.setStyleSheet("QGroupBox { border: 150px ; border-radius: 150px; padding: 0px 0px 0px 0px;} ")
		print(self.size())
		# QGroupBox::title { border: 0px ; border-radius: 0px; padding: 0px 0px 0px 0px; margin = 0px 0px 0px 0px } 
		# self.setMinimumSize(QtCore.QSize(0, 0))
		# self.setMaximumSize(QtCore.QSize(30, 30))
		# self.setStyleSheet(f"QGroupBox {{background-color: {grey_clear};}}")
class customFrame(QtWidgets.QGroupBox):
	def __init__(self, Text = None, color=None, height=None ):
		super(customFrame, self).__init__()
		if Text is not None:
			self.setTitle(Text)
		if color is not None:
			self.setStyleSheet(f"QGroupBox {{background-color: {color}; border: 2px solid {color};}}")
		self.setFlat(True)

		if height is not None :
			self.setMaximumHeight(height)
		
		self.setMinimumSize(1,1)
		# print(self.size())
		# self.setAlignment(QtCore.Qt.AlignTop)

class customFrame_02(QtWidgets.QFrame):
	def __init__(self, color=None, height=None ):
		super(customFrame_02, self).__init__()

		self.setMinimumSize(1,1)
		if height is not None :
			self.setMaximumHeight(height)
		# print(self.size())
		# self.setAlignment(QtCore.Qt.AlignTop)

class addSubtractList(QtWidgets.QWidget):
	def __init__(self, Text = None, ):
		super(addSubtractList, self).__init__()
		self.Text = Text
		self.create_widgets()
		self.create_layout()
		
	def create_widgets(self):
		self.listWidget = QtWidgets.QListWidget()
		self.listWidget.setStyleSheet(f"QListWidget {{background-color: {grey_black}; border: 10px solid {grey_black};}}")
		# self.listWidget.setMaximumHeight(350)
		# self.listWidget.setSize(350)
		
		

		self.addButton = SquareButton("+")
		self.subtractButton = SquareButton("-")
		self.clearButton = deleteButton("x")

		self.listGroupBox = QtWidgets.QGroupBox(self.Text)
		# self.listGroupBox.setMaximumHeight(350)

	def create_layout(self):
		self.main_Vlayout = QtWidgets.QVBoxLayout(self.listGroupBox)
		self.main_Vlayout.setAlignment(QtCore.Qt.AlignTop)
		# self.main_Vlayout.setContentsMargins(0,0,0,0)
		# self.main_Vlayout.setSpacing(0)
		self.button_HLayout = QtWidgets.QHBoxLayout()
		# self.button_HLayout.setAlignment(QtCore.Qt.AlignTop)
		self.main_Vlayout.addLayout(self.button_HLayout)

		self.button_HLayout.addWidget(self.addButton)
		self.button_HLayout.addWidget(self.subtractButton)
		self.button_HLayout.addWidget(self.clearButton)

		self.main_Vlayout.addWidget(self.listWidget)
		self.setLayout(self.main_Vlayout)

	def add_list_item(self, item):
		self.listWidget.addItem(item)

class LabelTitle(QtWidgets.QLabel):
	def __init__(self, name="LOREM", size = 16, bold = False,   line_height = line_height, line_width = line_height ):
		super(LabelTitle, self).__init__()

		# self.setFixedSize(line_height, line_width)
		# self.setStyleSheet(
		# 	f"QWidget{{background-color: {orange_dark}; border-style: none;}}")

		# self.label = QtWidgets.QLabel(self.container_widget) 
		self.setText(name)
		# self.setFixedSize(line_height, line_width)

		# self.setFont(basic_font(size,bold))
		self.setAlignment(QtCore.Qt.AlignLeft)

class scrollButton(QtWidgets.QPushButton):
	def __init__(self, parent=None, Text="Manager", ):
		super(scrollButton, self).__init__()
		self.setText(Text)
		self.setFlat(True)
		# self.setMinimumSize(QtCore.QSize(0, 0))
		# self.setMaximumSize(QtCore.QSize(20, 20))
		self.setStyleSheet(f"QPushButton {{background-color: {orange_light};}}")

class ColorDisplayButton(QtWidgets.QPushButton):
	def __init__(self, parent=None, color = red_warning):
		super(ColorDisplayButton, self).__init__()
		# self.setFlat(True)
		self.setMinimumSize(QtCore.QSize(0, 0))
		self.setMaximumSize(QtCore.QSize(50, 50))
		r, g, b = color[:3]
		self.setStyleSheet(f"QPushButton {{background-color: rgb({r}, {g}, {b});}}")

class CustomTreeWidget(QtWidgets.QTreeWidget):
	def __init__(self, parent=None):
		super(CustomTreeWidget, self).__init__()
		
		self.setHeaderHidden(True)
		self.setMinimumHeight(50)
		self.setSizeAdjustPolicy(QtWidgets.QAbstractScrollArea.AdjustToContents)
		self.setStyleSheet(f"QTreeWidget {{background-color: {grey_black}; border: 1px solid {grey_black};}}")
		
		# self.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
		# self.setStyleSheet(f"QTreeWidget {{background-color: {grey_clear};}}")
class TreeWidget(QtWidgets.QTreeWidget):

	def __init__(self, h = 550,w= 10,column_width = 1000):
		super(TreeWidget, self).__init__()
		self.setMinimumSize(w,h)
		self.setHeaderHidden(True)
		self.setStyleSheet(
			f"QTreeWidget{{background-color: {grey_black}; border-style: none;}}"
			# f"QScrollBar:vertical {{ background-color: {orange_dark}; }}"
			f"QScrollBar::handle:horizontal{{background-color: {grey_light}; height: 20px;}}"
			f"QScrollBar::handle:vertical{{background-color: {grey_light}; height: 20px;}}"
			)
		self.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
		self.setHorizontalScrollMode(QtWidgets.QAbstractItemView.ScrollPerPixel)
		
		self.setColumnCount(1)
		
		first_column_width = column_width  # Adjust the width as needed
		self.setColumnWidth(0, first_column_width)
		# self.resizeColumnToContents(0)
		# self.header().setSectionResizeMode(QtWidgets.QHeaderView.Fixed)  # Fix the column width
		# self.header().setStretchLastSection(False)



class SeparatorWidget(QtWidgets.QFrame):
	def __init__(self,parent=None, color=grey_light,Height = 4, Width = 4):
		super(SeparatorWidget, self).__init__()
		self.setStyleSheet(f"QFrame {{background-color: {color};}}")
		self.setFrameShape(QtWidgets.QFrame.HLine)
		self.setMinimumHeight(Height)
		self.setMinimumWidth(Width)
		self.setContentsMargins(1,1,1,1)

class CustomTabWidget(QtWidgets.QTabWidget):
	def __init__(self,):
		super(CustomTabWidget, self).__init__()
		self.setStyleSheet(f"QTabBar::tab {{min-width: 50px;min-height: 50px;}}")
		#
		self.setTabPosition(QtWidgets.QTabWidget.North)

class LabelTagWidget(QtWidgets.QWidget):

    def __init__(self, name):
        super(LabelTagWidget, self).__init__()
        self.container_widget = QtWidgets.QHBoxLayout(self)
        self.label = QtWidgets.QLabel() 
        self.label.setText(name)
        self.label.setFixedHeight(line_height)
        self.label.setFont(basic_font(10,False))
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.container_widget.addWidget(self.label)
		
class LabelButtonWidget(QtWidgets.QWidget):
	clicked = QtCore.Signal()  # Custom signal

	def __init__(self, name, icon):
		super(LabelButtonWidget, self).__init__()

		
		# # Get the directory of the current script
		# # script_dir = os.path.dirname(os.path.dirname(__file__))
		# script_dir = "E:\Personnal_Project\Maya\Addon\SK_creator"
		# # Construct the path to the icons folder
		# icons_dir = os.path.join(script_dir, "icons")

		# # Construct the path to a specific icon file
		icon_path = get_icon()

		# Create the label and button
		self.label = QtWidgets.QLabel()
		self.button = QtWidgets.QPushButton(name)

		# Create the main layout
		main_layout = QtWidgets.QVBoxLayout(self)
		main_layout.setSpacing(0)
		main_layout.setContentsMargins(0, 0, 0, 0)

		# Create a container widget for the label and button
		container_widget = QtWidgets.QWidget(self)
		# container_widget.setMinimumSize(150, line_height)
		# container_widget.setMaximumSize(950, line_height)
		container_widget.setFixedSize(150+line_height, line_height)
		container_layout = QtWidgets.QHBoxLayout(container_widget)
		container_layout.setSpacing(0)
		container_layout.setContentsMargins(0, 0, 0, 0)

		# Create the label
		self.label = QtWidgets.QLabel() 
		
		self.label.setAlignment(QtCore.Qt.AlignCenter)
		self.icon = QtGui.QIcon(icon_path)

		pixmap = self.icon.pixmap(QtCore.QSize(line_height-11, line_height-11))

		self.label.setPixmap(pixmap)
		self.label.setFixedSize(line_height,line_height)

		# Create the button
		self.button = QtWidgets.QPushButton(name)
		self.button.setFont(basic_font(10,False))
		# self.button.setStyleSheet("""
		# QPushButton {
		#     background-color: rgb(95, 95, 95);text-align: center;;
		# }

		# QPushButton:hover {
		#     background-color: rgb(110, 110, 110);
		# }
		# """)
		self.button.setStyleSheet(
			f"QPushButton{{background-color: {grey_light}; border-style: none;text-align: center;}}"
			f"QPushButton:hover {{background-color: {grey_clear}; border-style: none;}}"
			)
		
		# self.button.setMinimumSize(150,line_height)
		# self.button.setMaximumSize(1000, line_height)
		self.button.setFixedSize(150, line_height)
		# Add the label and button to the container layout
		container_layout.addWidget(self.label)
		container_layout.addWidget(self.button)

		# Set the background color of the container widget
		container_widget.setStyleSheet(f"QWidget{{background-color: {grey_black}; border-style: none;text-align: center;}}")


		# Add the container widget to the main layout
		main_layout.addWidget(container_widget)

	def emitClicked(self):
		self.clicked.emit()  # Emit the custom clicked signal
		
# class LabelWidget(QtWidgets.QWidget):

# 	def __init__(self, name="LOREM", size = 16, bold = False,   line_height = line_height, line_width = line_height ):
# 		super(LabelWidget, self).__init__()

# 		self.container_widget = QtWidgets.QWidget(self)
# 		self.container_widget.setFixedSize(line_height, line_width)
# 		self.container_widget.setStyleSheet(
# 			f"QWidget{{background-color: {orange_dark}; border-style: none;}}")

# 		self.label = QtWidgets.QLabel(self.container_widget) 
# 		self.label.setText(name)
# 		self.label.setFixedSize(line_height, line_width)

# 		self.label.setFont(basic_font(size,bold))
# 		self.label.setAlignment(QtCore.Qt.AlignLeft)

class LineEditWidget(QLineEdit):
	def __init__(self, parent=None, color = grey_black, h=line_height, w=20):
		super(LineEditWidget, self).__init__()
		self.setMinimumSize(w,h)
		# self.setFixedHeight(line_height)
		self.setFont(basic_font(12, False))
		self.setStyleSheet(f"QLineEdit{{background-color: {color}; border-style: none;padding: 6px}}")
class labelWidget(QtWidgets.QLabel):
	def __init__(self, text= "BLANK"):
		super(labelWidget, self).__init__()
		self.setText(text)
		self.setFont(basic_font(12, False))
		# self.setStyleSheet(f"QLineEdit{{background-color: {color}; border-style: none;padding: 6px}}")

class CheckBoxWidget(QtWidgets.QWidget):
	clicked = QtCore.Signal()  # Custom signal
	def __init__(self, name, state=True):
		super(CheckBoxWidget, self).__init__()
		# Create the main layout
		main_layout = QtWidgets.QVBoxLayout(self)
		main_layout.setSpacing(10)
		main_layout.setContentsMargins(0, 0, 0, 0)


		container_widget = QtWidgets.QWidget(self)
		container_widget.setMinimumSize(0, line_height)
		container_widget.setMaximumSize(10000, line_height)
		container_layout = QtWidgets.QHBoxLayout(container_widget)


		container_layout.setSpacing(25)
		container_layout.setContentsMargins(25, 5, 25, 5)

		# Create the radio button group
		self.checkBox = QtWidgets.QCheckBox(name)
		container_layout.addWidget(self.checkBox)
		self.checkBox.setChecked(state)

		self.checkBox.setStyleSheet(f"QCheckBox::indicator:checked {{background-color: {orange_light}; border-style: none;}}"
		f"QCheckBox::indicator:hover {{background-color: {grey_light}; border-style: none;border: 0.2em solid {orange_dark}}}"
		f"QCheckBox::indicator {{background-color: {grey_light}; border-style: none;}}")
		# self.checkBox.setText(name)


		# Create the radio buttons and add them to the button group

		# Set the background color of the container widget
		container_widget.setStyleSheet(f"QWidget{{background-color: {grey_black}; border-style: none;text-align: center;}}")

		main_layout.addWidget(container_widget)

	def emitClicked(self):
		self.clicked.emit()  # Emit the custom clicked signal
	# def __init__(self, name):
	# 	super(CheckBoxWidget, self).__init__()
	# 	main_layout = QtWidgets.QHBoxLayout(self)
	# 	main_layout.setSpacing(0)
	# 	main_layout.setContentsMargins(0, 0, 0, 0)

	# 	container_widget = QtWidgets.QWidget()
	# 	container_widget.setFixedSize(line_height,line_height)
	# 	container_widget.setContentsMargins(0, 0, 0, 0)

	# 	self.checkBox = QtWidgets.QCheckBox(name,container_widget,) 
	# 	# self.checkBox.setText(name)
		
	# 	self.checkBox.setStyleSheet(
	# 		f"QCheckBox{{color: {grey_black};}}"
	# 		f"QCheckBox::indicator{{background-color: {orange_light};border: 0.25em solid {orange_light};}}"
	# 		f"QCheckBox::indicator:unchecked {{background-color:{grey_dark};border: 0.1em solid {grey_black};}}"
	# 		)
	# 	container_widget.setStyleSheet(f"QWidget{{background-color: {grey_black}; border-style: none;}}")
	# 	main_layout.setAlignment(QtCore.Qt.AlignCenter)
	# 	container_layout = QtWidgets.QHBoxLayout(container_widget)
	# 	container_layout.setAlignment(QtCore.Qt.AlignCenter)
	# 	container_layout.setSpacing(0)
	# 	container_layout.setContentsMargins(0, 0, 0, 0)
	# 	container_layout.addWidget(self.checkBox)

	# 	main_layout.addWidget(container_widget)
class CheckBoxWidget_02(QtWidgets.QCheckBox):
	def __init__(self, state=True, name="HAHA"):
		super(CheckBoxWidget_02, self).__init__()
		self.setChecked(state)
		self.setText(name)
		self.setStyleSheet(
			f"QCheckBox{{color: {grey_black};}}"
			f"QCheckBox::indicator{{background-color: {orange_light};border: 0.25em solid {orange_light};}}"
			f"QCheckBox::indicator:unchecked {{background-color:{grey_dark};border: 0.1em solid {grey_black};}}"
			)

class ScrollArea(QtWidgets.QScrollArea):
	def __init__(self):
		super().__init__()

		# Create a QWidget to hold the content
		content_widget = QtWidgets.QWidget()
		self.setWidget(content_widget)
		# content_widget.setMinimumSize(self.sizeHint())
		# content_widget.adjustSize()
		# sizeHint  = content_widget.sizeHint()
		# content_widget.setMinimumSize(sizeHint)

		# Set properties for the QScrollArea
		self.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
		self.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)
		self.setWidgetResizable(True)
		self.setAlignment(QtCore.Qt.AlignTop)

		# Apply your styles
		self.setStyleSheet(
			f"QScrollArea{{background-color: {grey_black}; border-style: none;}}"
			f"QScrollBar:vertical {{ background-color: {orange_dark}; }}"
			f"QScrollBar:horizontal {{ background-color: {orange_dark}; }}"
			f"QScrollBar::handle:horizontal{{background-color: {grey_light}; height: 20px;}}"
			f"QScrollBar::handle:vertical{{background-color: {grey_light}; height: 20px;}}"
			)

		content_layout = QtWidgets.QVBoxLayout(content_widget)
		# content_layout.setContentsMargins(0, 0, 0, 0)
		# content_layout.setSpacing(0)

		content_widget.setSizePolicy(
			QtWidgets.QSizePolicy.MinimumExpanding, QtWidgets.QSizePolicy.MinimumExpanding
		)

	def addWidget(self, widget):
		content_layout = self.widget().layout()
		content_layout.addWidget(widget)

	def addItem(self, item):
		content_layout = self.widget().layout()
		content_layout.addItem(item)
	def addLayout(self, layout):
		content_layout = self.widget().layout()
		content_layout.addLayout(layout)
		# self.setMinimumSize(self.sizeHint())
		# self.adjustSize()
		# sizeHint  = self.sizeHint()
		# self.setMinimumSize(sizeHint)
		# # layout.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
		# print("Scrooll",sizeHint )

# class SquareButton(QtWidgets.QWidget):
# 	clicked = QtCore.Signal()

# 	def __init__(self, text, icon_path):
# 		super().__init__()

# 		# Create the main layout
# 		main_layout = QtWidgets.QVBoxLayout(self)
# 		main_layout.setSpacing(0)
# 		main_layout.setContentsMargins(0, 0, 0, 0)

# 		container_widget = QtWidgets.QWidget(self)

# 		container_widget.setFixedSize(line_height, line_height)
# 		container_layout = QtWidgets.QHBoxLayout(container_widget)
# 		container_layout.setSpacing(0)
# 		container_layout.setContentsMargins(0, 0, 0, 0)

# 		self.button = QtWidgets.QPushButton(text)
# 		self.button.setFont(basic_font(10,False))
# 		self.button.setStyleSheet(
# 			f"QPushButton{{background-color: {grey_light}; border-style: none;text-align: center;}}"
# 			f"QPushButton:hover {{background-color: {grey_clear}; border-style: none;}}"
# 			)

# 		icon = ":/"+ icon_path
		
# 		self.button.setIcon(QIcon(icon))

# 		self.button.setFixedSize(line_height, line_height)
# 		container_layout.addWidget(self.button)

# 		container_widget.setStyleSheet(f"QWidget{{background-color: {grey_black}; border-style: none;text-align: center;}}")

# 		main_layout.addWidget(container_widget)

# 		self.button.clicked.connect(self.emitClicked)

	def emitClicked(self):
		# Emit the custom clicked signal when the button is clicked
		self.clicked.emit()

class Header(QtWidgets.QWidget):
	"""Header class for collapsible group"""

	def __init__(self, name, content_widget):
		"""Header Class Constructor to initialize the object.

		Args:
			name (str): Name for the header
			content_widget (QtWidgets.QWidget): Widget containing child elements
		"""
		super(Header, self).__init__()
		self.content = content_widget
		self.expand_ico = QtGui.QPixmap(":teDownArrow.png")
		self.collapse_ico = QtGui.QPixmap(":teRightArrow.png")
		self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
		

		stacked = QtWidgets.QStackedLayout(self)
		stacked.setStackingMode(QtWidgets.QStackedLayout.StackAll)
		background = QtWidgets.QLabel()
		# background.setStyleSheet("QLabel{ background-color: rgb(93, 93, 93); border-radius:2px}")
		background.setStyleSheet(f"QLabel{{background-color: {grey_light}; border-style: none;}}")
		

		widget = QtWidgets.QWidget()

		layout = QtWidgets.QHBoxLayout(widget)

		self.icon = QtWidgets.QLabel()
		self.icon.setStyleSheet(f"QLabel{{background-color: {grey_light}; border-style: none;}}")
		self.icon.setPixmap(self.expand_ico)
		
		layout.addWidget(self.icon)
		layout.setContentsMargins(11, 0, 11, 0)
		

		font = QtGui.QFont()
		font.setBold(True)
		label = QtWidgets.QLabel(name)
		label.setFont(font)
		label.setStyleSheet(f"QLabel{{background-color: {grey_light}; border-style: none;}}")
		

		layout.addWidget(label)
		layout.addItem(QtWidgets.QSpacerItem(0, 0, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding))

		stacked.addWidget(widget)
		stacked.addWidget(background)
		background.setMinimumHeight(layout.sizeHint().height() * 1.5)

	def mousePressEvent(self, *args):
		"""Handle mouse events, call the function to toggle groups"""
		self.expand() if not self.content.isVisible() else self.collapse()

	def expand(self):
		self.content.setVisible(True)
		self.icon.setPixmap(self.expand_ico)
		

	def collapse(self):
		self.content.setVisible(False)
		self.icon.setPixmap(self.collapse_ico)



class Container(QtWidgets.QWidget):
	"""Class for creating a collapsible group similar to how it is implement in Maya

		Examples:
			Simple example of how to add a Container to a QVBoxLayout and attach a QGridLayout

			>>> layout = QtWidgets.QVBoxLayout()
			>>> container = Container("Group")
			>>> layout.addWidget(container)
			>>> content_layout = QtWidgets.QGridLayout(container.contentWidget)
			>>> content_layout.addWidget(QtWidgets.QPushButton("Button"))
	"""
	def __init__(self, name, color_background=True):
		"""Container Class Constructor to initialize the object

		Args:
			name (str): Name for the header
			color_background (bool): whether or not to color the background lighter like in maya
		"""
		super(Container, self).__init__()
		# groupBox = customFrame()
		layout = QtWidgets.QVBoxLayout(self)
		layout.setContentsMargins(0, 0, 0, 0)
		layout.setSpacing(0)

		
		
		self._content_widget = QtWidgets.QFrame()
		self._content_widget.setStyleSheet(f"QFrame {{background-color: {grey_dark}; border: 1px solid {grey_dark};}}")
		
		# self._sizehint = QtCore.QSize(width, height)
		# self._content_widget.setMinimumSize(500,500)
		# self._content_widget.resize(200,200)

		
		
		
		# self._content_widget.setStyleSheet(f"QGroupBox {{background-color: {grey_black}; border: 1px solid {grey_light};}}")
		# self._content_widget = customFrame(color = grey_light)
		# self._content_widget.setContentsMargins(0, 0, 0, 0)
		# if color_background:
		# 	self._content_widget.setStyleSheet(".QWidget{background-color: rgb(73, 73, 73); "
		# 									   "margin-left: 2px; margin-right: 2px}")
		header = Header(name, self._content_widget)
		layout.addWidget(header)
		layout.addWidget(self._content_widget)
		# self._content_widget.setMinimumSize(self._content_widget.sizeHint())
		# self._content_widget.adjustSize()
		# sizeHint  = layout.sizeHint()*10
		# self._content_widget.setMinimumSize(sizeHint)
		# print("HAAAA",sizeHint )
		
		


		# assign header methods to instance attributes so they can be called outside of this class
		self.collapse = header.collapse
		self.expand = header.expand
		self.toggle = header.mousePressEvent

	@property
	def contentWidget(self):
		"""Getter for the content widget

		Returns: Content widget
		"""
		return self._content_widget
	


# class MainWindowDockable(MayaQWidgetDockableMixin, QDialog):
# toolname = 'vcToolset'

# def __init__(self, parent=None):
#     self.deleteInstances()
#     super(MainWindowDockable, self).__init__(parent)
#     mayaMainWindowPtr = omui.MQtUtil.mainWindow()
#     self.mayaMainWindow = wrapInstance(
#         long(mayaMainWindowPtr), QMainWindow)
#     #self.setObjectName(MainWindowDockable.toolname)
#     self.basicTools = None
#     self._callback = None
#     self.setWindowFlags(Qt.Window)
#     self.setWindowTitle('VC Tools')
#     self.setMinimumSize(260, 235)
#     self.resize(275, 700)
#     self.setAttribute(Qt.WA_DeleteOnClose)
#     self.layout = QVBoxLayout()
#     self.setLayout(self.layout)
#     self.setChangeCallback()

#     self.setObjectName("VCToolsDockable")


# def dockCloseEventTriggered(self):
#     self.deleteInstances()
#     om.MMessage.removeCallback(self._callback)

# def deleteInstances(self):
#     mayaMainWindowPtr = omui.MQtUtil.mainWindow()
#     mayaMainWindow = wrapInstance(long(mayaMainWindowPtr), QMainWindow)

#     for child in mayaMainWindow.children():
#         if type(child) == MayaQDockWidget:
#             if child.widget().objectName() == MainWindowDockable.toolname:
#                 mayaMainWindow.removeDockWidget(child)
#                 child.setParent(None)
#                 child.deleteLater()

# def deleteControl(self, control):
#     if cmds.workspaceControl(control, q=True, exists=True):
#         cmds.workspaceControl(control, e=True, close=True)
#         cmds.deleteUI(control, control=True)

# def setChangeCallback(self):
#     self._callback = om.MEventMessage.addEventCallback("SelectionChanged", refreshCurVertVal)